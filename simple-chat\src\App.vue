<!--
  Главный компонент чата
  Vue.js использует Single File Components (SFC) - файлы с расширением .vue
  Каждый компонент состоит из трех секций: template, script, style
-->

<template>
  <!--
    Template - HTML разметка компонента
    Здесь мы определяем структуру нашего чата
  -->
  <div class="chat-app">
    <!-- Заголовок чата -->
    <div class="chat-header">
      <h1>💬 Простой чат на Vue.js</h1>
      <p>Онлайн: {{ onlineUsers }} пользователей</p>
    </div>

    <!-- Область сообщений -->
    <div class="chat-messages" ref="messagesContainer">
      <!--
        v-for - директива Vue для циклов
        Отображаем каждое сообщение из массива messages
        :key - уникальный ключ для оптимизации рендеринга
      -->
      <div
        v-for="message in messages"
        :key="message.id"
        :class="['message', message.type]"
      >
        <div class="message-content">
          <strong class="message-author">{{ message.author }}:</strong>
          <span class="message-text">{{ message.text }}</span>
        </div>
        <div class="message-time">{{ message.time }}</div>
      </div>

      <!--
        Индикатор печати (показывается когда кто-то печатает)
        v-if - условное отображение элемента
      -->
      <div v-if="isTyping" class="typing-indicator">
        <div class="typing-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <span class="typing-text">Бот печатает...</span>
      </div>
    </div>

    <!-- Форма для ввода сообщений -->
    <div class="chat-input">
      <!--
        v-model - двустороннее связывание данных
        @keyup.enter - обработчик события нажатия Enter
        :disabled - динамическое отключение поля
      -->
      <input
        v-model="newMessage"
        @keyup.enter="sendMessage"
        :disabled="isTyping"
        class="message-input"
        placeholder="Введите ваше сообщение..."
        maxlength="500"
      />

      <!--
        @click - обработчик клика
        :disabled - кнопка отключается если сообщение пустое или бот печатает
      -->
      <button
        @click="sendMessage"
        :disabled="!newMessage.trim() || isTyping"
        class="send-button"
      >
        📤 Отправить
      </button>
    </div>
  </div>
</template>
<script>
/*
  Script секция - JavaScript логика компонента
  Здесь мы определяем данные, методы и жизненный цикл компонента
*/

// Импортируем функции из Vue
import { nextTick } from 'vue'

export default {
  name: 'ChatApp', // Имя компонента

  // Data - реактивные данные компонента
  data() {
    return {
      // Массив всех сообщений в чате
      messages: [
        {
          id: 1,
          text: 'Добро пожаловать в чат! Напишите что-нибудь 👋',
          author: 'Система',
          type: 'system',
          time: this.getCurrentTime()
        }
      ],

      // Текст нового сообщения (связан с input через v-model)
      newMessage: '',

      // Флаг показывающий, что кто-то печатает
      isTyping: false,

      // Счетчик для генерации уникальных ID сообщений
      messageCounter: 1,

      // Имя текущего пользователя
      currentUser: 'Пользователь',

      // Количество онлайн пользователей (имитация)
      onlineUsers: Math.floor(Math.random() * 10) + 1,

      // Массив возможных ответов бота
      botResponses: [
        'Очень интересно! 🤔',
        'Расскажите больше об этом 😊',
        'Согласен с вами! 👍',
        'Это звучит здорово! 🎉',
        'Хм, а что вы думаете по этому поводу? 💭',
        'Отличная мысль! 💡',
        'Спасибо за информацию! 🙏',
        'Не знал об этом, очень познавательно! 📚',
        'Продолжайте, мне интересно! ✨',
        'Вау, впечатляет! 😮'
      ]
    }
  },

  // Methods - методы компонента
  methods: {
    // Метод отправки сообщения
    sendMessage() {
      // Проверяем, что сообщение не пустое (убираем пробелы с краев)
      if (!this.newMessage.trim()) {
        return
      }

      // Создаем объект нового сообщения пользователя
      const userMessage = {
        id: ++this.messageCounter, // Увеличиваем счетчик и присваиваем ID
        text: this.newMessage.trim(), // Текст сообщения без лишних пробелов
        author: this.currentUser, // Автор сообщения
        type: 'user', // Тип сообщения (для стилизации)
        time: this.getCurrentTime() // Время отправки
      }

      // Добавляем сообщение в массив messages
      this.messages.push(userMessage)

      // Очищаем поле ввода
      this.newMessage = ''

      // Прокручиваем чат к последнему сообщению
      this.scrollToBottom()

      // Запускаем имитацию ответа бота
      this.simulateBotResponse()
    },

    // Имитация ответа бота
    simulateBotResponse() {
      // Показываем индикатор печати
      this.isTyping = true

      // Прокручиваем к индикатору печати
      setTimeout(() => {
        this.scrollToBottom()
      }, 100)

      // Генерируем случайную задержку от 1 до 3 секунд
      const delay = Math.random() * 2000 + 1000

      setTimeout(() => {
        // Скрываем индикатор печати
        this.isTyping = false

        // Выбираем случайный ответ из массива
        const randomResponse = this.getRandomBotResponse()

        // Создаем сообщение бота
        const botMessage = {
          id: ++this.messageCounter,
          text: randomResponse,
          author: 'Бот',
          type: 'bot',
          time: this.getCurrentTime()
        }

        // Добавляем сообщение бота в чат
        this.messages.push(botMessage)

        // Прокручиваем к последнему сообщению
        this.scrollToBottom()
      }, delay)
    },

    // Получение случайного ответа бота
    getRandomBotResponse() {
      const randomIndex = Math.floor(Math.random() * this.botResponses.length)
      return this.botResponses[randomIndex]
    },

    // Получение текущего времени в формате ЧЧ:ММ
    getCurrentTime() {
      const now = new Date()
      const hours = now.getHours().toString().padStart(2, '0')
      const minutes = now.getMinutes().toString().padStart(2, '0')
      return `${hours}:${minutes}`
    },

    // Прокрутка чата к последнему сообщению
    scrollToBottom() {
      // nextTick гарантирует, что DOM обновился перед прокруткой
      nextTick(() => {
        const container = this.$refs.messagesContainer
        if (container) {
          container.scrollTop = container.scrollHeight
        }
      })
    }
  },

  // Хуки жизненного цикла компонента
  mounted() {
    // Вызывается после монтирования компонента в DOM
    // Прокручиваем к последнему сообщению при загрузке
    this.scrollToBottom()

    // Фокусируемся на поле ввода
    nextTick(() => {
      const input = document.querySelector('.message-input')
      if (input) {
        input.focus()
      }
    })
  }
}
</script>
<style scoped>
/*
  Style секция - CSS стили компонента
  scoped означает, что стили применяются только к этому компоненту
*/

/* Основной контейнер чата */
.chat-app {
  display: flex;
  flex-direction: column;
  height: 100vh; /* Занимаем всю высоту экрана */
  max-width: 800px;
  margin: 0 auto;
  background: #ffffff;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* Заголовок чата */
.chat-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.chat-header h1 {
  margin: 0 0 5px 0;
  font-size: 1.5em;
}

.chat-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.9em;
}

/* Область сообщений */
.chat-messages {
  flex: 1; /* Занимает все доступное пространство */
  padding: 20px;
  overflow-y: auto; /* Прокрутка при переполнении */
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* Стили для сообщений */
.message {
  display: flex;
  flex-direction: column;
  max-width: 70%;
  animation: fadeIn 0.3s ease-in;
}

/* Анимация появления сообщений */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Сообщения пользователя (справа) */
.message.user {
  align-self: flex-end;
}

.message.user .message-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 18px 18px 5px 18px;
}

/* Сообщения бота (слева) */
.message.bot {
  align-self: flex-start;
}

.message.bot .message-content {
  background: #e9ecef;
  color: #333;
  border-radius: 18px 18px 18px 5px;
}

/* Системные сообщения (по центру) */
.message.system {
  align-self: center;
  max-width: 90%;
}

.message.system .message-content {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
  border-radius: 15px;
  text-align: center;
}

/* Содержимое сообщения */
.message-content {
  padding: 12px 16px;
  word-wrap: break-word;
  line-height: 1.4;
}

.message-author {
  font-size: 0.9em;
  margin-right: 5px;
}

.message-text {
  font-size: 1em;
}

/* Время сообщения */
.message-time {
  font-size: 0.75em;
  opacity: 0.7;
  margin-top: 5px;
  padding: 0 5px;
}

.message.user .message-time {
  text-align: right;
}

.message.bot .message-time {
  text-align: left;
}

.message.system .message-time {
  text-align: center;
}
/* Индикатор печати */
.typing-indicator {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #e9ecef;
  border-radius: 18px 18px 18px 5px;
  max-width: 150px;
  align-self: flex-start;
}

.typing-dots {
  display: flex;
  margin-right: 10px;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #999;
  margin: 0 2px;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }
.typing-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.typing-text {
  font-size: 0.9em;
  color: #666;
  font-style: italic;
}

/* Форма ввода сообщений */
.chat-input {
  display: flex;
  padding: 20px;
  background: white;
  border-top: 1px solid #e9ecef;
  gap: 10px;
}

.message-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 25px;
  outline: none;
  font-size: 1em;
  font-family: inherit;
  transition: border-color 0.3s ease;
}

.message-input:focus {
  border-color: #667eea;
}

.message-input:disabled {
  background: #f8f9fa;
  cursor: not-allowed;
}

.send-button {
  padding: 12px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1em;
  font-family: inherit;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.send-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.send-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Адаптивность для мобильных устройств */
@media (max-width: 768px) {
  .chat-app {
    height: 100vh;
    max-width: 100%;
  }

  .message {
    max-width: 85%;
  }

  .chat-input {
    padding: 15px;
  }

  .send-button {
    padding: 12px 16px;
    font-size: 0.9em;
  }
}

/* Кастомная прокрутка для области сообщений */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
